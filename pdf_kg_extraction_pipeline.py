"""
Complete Knowledge Graph Extraction Pipeline for 25 PDFs
This script orchestrates the full pipeline from PDF processing to Neo4j import.
"""

import os
import time
from datetime import datetime
from setup_llm_generator_direct import setup_gemini_llm_generator_direct
from setup_embedding_model import setup_qwen_embedding_model
from setup_processing_config import create_processing_config, create_extraction_pipeline, create_output_directories
from atlas_rag.vectorstore.create_graph_index import create_embeddings_and_index
from atlas_rag.retriever.hipporag2 import HippoRAG2Retriever
from atlas_rag.retriever.inference_config import InferenceConfig

def run_knowledge_extraction_pipeline(dataset_name="pdf_dataset", filename_pattern=""):
    """
    Run the complete knowledge graph extraction pipeline.
    
    Args:
        dataset_name (str): Name for the dataset
        filename_pattern (str): Pattern to match input files
    """
    
    print("🚀 Starting AutoSchemaKG Pipeline for 25 PDFs")
    print("=" * 60)
    print(f"Dataset: {dataset_name}")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    start_time = time.time()
    
    try:
        # Step 1: Initialize models
        print("1️⃣ Initializing Models...")
        print("-" * 30)
        
        llm_generator = setup_gemini_llm_generator_direct()
        sentence_encoder = setup_qwen_embedding_model()
        
        print("✅ Models initialized successfully\n")
        
        # Step 2: Setup configuration
        print("2️⃣ Setting up Processing Configuration...")
        print("-" * 30)
        
        create_output_directories(dataset_name)
        processing_config = create_processing_config(dataset_name, filename_pattern)
        
        # Apply AFC-optimized batch size for concept generation (like robust script)
        processing_config.batch_size_concept = 10
        print(f"🔧 Applied AFC-optimized batch_size_concept: {processing_config.batch_size_concept}")
        
        kg_extractor = create_extraction_pipeline(llm_generator, processing_config)
        
        print("✅ Configuration setup complete\n")
        
        # Step 3: Triple extraction
        print("3️⃣ Extracting Triples from PDFs...")
        print("-" * 30)
        print("🔍 This step extracts entities, relations, and events from your PDF content")
        print("🎯 Using quality-first configuration with larger batches for better context")
        print("⏱️  Expected time: 30-45 minutes for 25 PDFs (quality processing)")
        
        extraction_start = time.time()
        kg_extractor.run_extraction()
        extraction_time = time.time() - extraction_start
        
        print(f"✅ Triple extraction completed in {extraction_time/60:.1f} minutes\n")
        
        # Step 4: Convert to CSV
        print("4️⃣ Converting to CSV Format...")
        print("-" * 30)
        print("🔄 Converting extracted JSON data to CSV format for Neo4j import...")
        
        kg_extractor.convert_json_to_csv()
        print("✅ CSV conversion completed - Files available in triples_csv/ directory\n")
        
        # Step 5: Generate concepts
        print("5️⃣ Generating Concepts...")
        print("-" * 30)
        print("🧠 Creating higher-level concepts from extracted triples")
        print(f"📊 Using batch size of {processing_config.batch_size_concept} for comprehensive concept generation")
        
        concept_start = time.time()
        kg_extractor.generate_concept_csv()
        concept_time = time.time() - concept_start
        
        print(f"✅ Concept generation completed in {concept_time/60:.1f} minutes\n")
        
        # Step 6: Create concept CSV
        print("6️⃣ Creating Concept CSV Files...")
        print("-" * 30)
        print("🔗 Merging concept data with extracted triples...")
        
        kg_extractor.create_concept_csv()
        print("✅ Concept CSV files created in concept_csv/ directory\n")
        
        # Step 7: Add numeric IDs
        print("7️⃣ Adding Numeric IDs for Neo4j...")
        print("-" * 30)
        
        kg_extractor.add_numeric_id()
        print("✅ Numeric IDs added\n")
        
        # Step 8: Convert to GraphML
        print("8️⃣ Converting to GraphML...")
        print("-" * 30)
        
        kg_extractor.convert_to_graphml()
        print("✅ GraphML conversion completed\n")
        
        # Step 9: Generate embeddings
        print("9️⃣ Generating Embeddings with Qwen3-Embedding-4B...")
        print("-" * 30)
        print("🎯 Creating vector embeddings for semantic search")
        
        embedding_start = time.time()
        kg_extractor.compute_kg_embedding(sentence_encoder)
        embedding_time = time.time() - embedding_start
        
        print(f"✅ Embeddings generated in {embedding_time/60:.1f} minutes\n")
        
        # Step 10: Create FAISS indexes
        print("🔟 Creating FAISS Indexes...")
        print("-" * 30)
        
        kg_extractor.create_faiss_index()
        print("✅ FAISS indexes created\n")
        
        # Step 11: Generate import summary
        print("1️⃣1️⃣ Generating Import Summary...")
        print("-" * 30)
        
        generate_import_summary(dataset_name)
        
        total_time = time.time() - start_time
        
        print("🎉 PIPELINE COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print(f"Total processing time: {total_time/60:.1f} minutes")
        print(f"Dataset location: import/{dataset_name}/")
        print("🎯 Quality-first processing with original defaults completed")
        print()
        print("📋 Next Steps:")
        print("1. Review the generated files in import/{}/".format(dataset_name))
        print("2. Stop your Neo4j autoschemakg database")
        print("3. Run the Neo4j import command (see neo4j_import_commands.txt)")
        print("4. Start your Neo4j autoschemakg database")
        print("5. Test the knowledge graph with RAG queries")
        
    except Exception as e:
        print(f"❌ Pipeline failed with error: {str(e)}")
        print("\n🔧 Troubleshooting tips:")
        print("1. Check your API keys in config.ini")
        print("2. Ensure input files are in example_data/ directory")
        print("3. Verify internet connection for API calls")
        print("4. Check the logs for specific error messages")
        raise e

def generate_import_summary(dataset_name):
    """
    Generate a summary of files created and Neo4j import commands.
    
    Args:
        dataset_name (str): Name of the dataset
    """
    
    import_dir = f"import/{dataset_name}"
    
    # Count files
    csv_files = []
    index_files = []
    
    for root, dirs, files in os.walk(import_dir):
        for file in files:
            if file.endswith('.csv'):
                csv_files.append(os.path.join(root, file))
            elif file.endswith('.index'):
                index_files.append(os.path.join(root, file))
    
    print(f"📊 Import Summary for {dataset_name}:")
    print(f"  - CSV files created: {len(csv_files)}")
    print(f"  - FAISS indexes created: {len(index_files)}")
    
    # Generate Neo4j import command
    neo4j_command = generate_neo4j_import_command(dataset_name, csv_files)
    
    # Save import commands to file
    with open("neo4j_import_commands.txt", "w") as f:
        f.write("Neo4j Import Commands for AutoSchemaKG\n")
        f.write("=" * 50 + "\n\n")
        f.write("IMPORTANT: Stop your autoschemakg database before running this command!\n\n")
        f.write("Command to run in your Neo4j installation directory:\n")
        f.write("-" * 50 + "\n")
        f.write(neo4j_command)
        f.write("\n\nAfter import completes, start your autoschemakg database again.\n")
    
    print("✅ Neo4j import commands saved to neo4j_import_commands.txt")

def generate_neo4j_import_command(dataset_name, csv_files):
    """
    Generate the Neo4j admin import command.
    
    Args:
        dataset_name (str): Name of the dataset
        csv_files (list): List of CSV file paths
        
    Returns:
        str: Neo4j import command
    """
    
    # Categorize CSV files
    node_files = []
    relationship_files = []
    
    for file in csv_files:
        if 'nodes' in file or 'node' in file:
            node_files.append(file)
        elif 'edges' in file or 'edge' in file:
            relationship_files.append(file)
    
    # Build command
    command_parts = [
        "neo4j-admin database import full autoschemakg"
    ]
    
    # Add node files
    for node_file in node_files:
        command_parts.append(f"    --nodes ./{node_file}")
    
    # Add relationship files  
    for rel_file in relationship_files:
        command_parts.append(f"    --relationships ./{rel_file}")
    
    # Add options
    command_parts.extend([
        "    --overwrite-destination",
        "    --multiline-fields=true", 
        "    --id-type=string",
        "    --verbose",
        "    --skip-bad-relationships=true"
    ])
    
    return " \\\n".join(command_parts)

def validate_prerequisites():
    """
    Validate that all prerequisites are met before running the pipeline.
    
    Returns:
        bool: True if all prerequisites are met
    """
    
    print("🔍 Validating Prerequisites...")
    print("-" * 30)
    
    # Check config file
    if not os.path.exists('config.ini'):
        print("❌ config.ini not found")
        return False
    print("✅ config.ini found")
    
    # Check input directory
    if not os.path.exists('example_data'):
        print("❌ example_data directory not found")
        print("💡 Create this directory and place your JSON files there")
        return False
    
    # Check for input files
    input_files = [f for f in os.listdir('example_data') if f.endswith(('.json', '.txt'))]
    if len(input_files) == 0:
        print("❌ No input files found in example_data/")
        print("💡 Place your processed PDF files (JSON format) in example_data/")
        return False
    
    print(f"✅ Found {len(input_files)} input files")
    
    # Check import directory exists
    os.makedirs('import', exist_ok=True)
    print("✅ Import directory ready")
    
    print("✅ All prerequisites validated\n")
    return True

def setup_hipporag2_retriever(dataset_name="pdf_dataset", filename_pattern=""):
    """
    Setup HippoRAG2 retriever using generated GraphML and embeddings.
    
    Args:
        dataset_name (str): Name of the dataset
        filename_pattern (str): Pattern used for file naming
        
    Returns:
        HippoRAG2Retriever: Configured retriever ready for Q&A
    """
    
    print("🦛 Setting up HippoRAG2 Retriever...")
    print("=" * 50)
    
    # Initialize models (same as pipeline)
    llm_generator = setup_gemini_llm_generator_direct()
    sentence_encoder = setup_qwen_embedding_model()
    
    print("✅ Models initialized for HippoRAG2")
    
    # Setup paths
    working_directory = f"import/{dataset_name}"
    keyword = filename_pattern if filename_pattern else "from_json"
    model_name = sentence_encoder.model_name
    
    print(f"📁 Working directory: {working_directory}")
    print(f"🔑 Keyword: {keyword}")
    print(f"🤖 Embedding model: {model_name}")
    
    try:
        # Create embeddings and indexes using existing function
        print("\n📊 Creating embeddings and FAISS indexes...")
        hipporag_data = create_embeddings_and_index(
            sentence_encoder=sentence_encoder,
            model_name=model_name,
            working_directory=working_directory,
            keyword=keyword,
            include_events=True,
            include_concept=True,
            normalize_embeddings=True,
            text_batch_size=40,
            node_and_edge_batch_size=256
        )
        
        print("✅ Embeddings and indexes created successfully")
        
        # Setup inference config
        inference_config = InferenceConfig()
        inference_config.keyword = keyword
        
        # Initialize HippoRAG2 retriever
        print("\n🦛 Initializing HippoRAG2 retriever...")
        hipporag2_retriever = HippoRAG2Retriever(
            llm_generator=llm_generator,
            sentence_encoder=sentence_encoder,
            data=hipporag_data,
            inference_config=inference_config
        )
        
        print("✅ HippoRAG2 retriever initialized successfully!")
        
        # Display summary
        print(f"\n📊 HippoRAG2 Setup Summary:")
        print(f"  - Knowledge Graph Nodes: {len(hipporag_data['node_list']):,}")
        print(f"  - Knowledge Graph Edges: {len(hipporag_data['edge_list']):,}")
        print(f"  - Text Passages: {len(hipporag_data['text_dict']):,}")
        print(f"  - Embedding Dimension: {len(hipporag_data['node_embeddings'][0])}")
        print(f"  - Ready for Q&A: ✅")
        
        return hipporag2_retriever
        
    except Exception as e:
        print(f"❌ HippoRAG2 setup failed: {str(e)}")
        print("\n🔧 Troubleshooting tips:")
        print("1. Ensure the pipeline has completed successfully")
        print("2. Check that GraphML file exists in kg_graphml/ directory")
        print("3. Verify embedding files are present in the dataset directory")
        raise e

def hipporag2_interrogation(retriever):
    """
    Interactive Q&A interface using HippoRAG2.
    
    Args:
        retriever: HippoRAG2Retriever instance
    """
    
    print("\n🦛 HippoRAG2 Interactive Q&A Interface")
    print("=" * 50)
    print("Enter your questions about the knowledge graph.")
    print("Type 'quit', 'exit', or 'q' to stop.")
    print("Type 'help' for usage tips.")
    print()
    
    question_count = 0
    
    while True:
        try:
            # Get user input
            question = input("🤔 Your question: ").strip()
            
            # Handle exit commands
            if question.lower() in ['quit', 'exit', 'q']:
                print("\n👋 Thank you for using HippoRAG2! Goodbye!")
                break
                
            # Handle help
            if question.lower() == 'help':
                print("\n💡 HippoRAG2 Usage Tips:")
                print("- Ask questions about entities, relationships, or concepts in your knowledge graph")
                print("- HippoRAG2 uses personalized PageRank for multi-hop reasoning")
                print("- Try questions like: 'What is the relationship between X and Y?'")
                print("- Complex queries work well: 'How does A connect to B through C?'")
                print()
                continue
                
            # Skip empty questions
            if not question:
                print("⚠️  Please enter a question.")
                continue
                
            question_count += 1
            print(f"\n🔍 Processing question #{question_count}...")
            
            # Retrieve relevant passages using HippoRAG2
            start_time = time.time()
            passages = retriever.retrieve_passages(question)
            retrieval_time = time.time() - start_time
            
            print(f"⚡ Retrieved {len(passages)} passages in {retrieval_time:.2f}s")
            
            # Generate answer using the LLM with retrieved context
            context = "\n".join([f"Passage {i+1}: {passage}" for i, passage in enumerate(passages)])
            
            llm_start = time.time()
            answer = retriever.llm_generator.generate_response(
                batch_messages=[
                    {"role": "system", "content": "You are a helpful assistant. Use the provided context to answer questions accurately."},
                    {"role": "user", "content": f"Question: {question}\n\nContext:\n{context}"}
                ],
                max_new_tokens=1024,
                temperature=0.1
            )
            llm_time = time.time() - llm_start
            
            # Display results
            print(f"\n📝 Answer (generated in {llm_time:.2f}s):")
            print("-" * 30)
            print(answer)
            print()
            
            # Show retrieved passages
            if len(passages) > 0:
                print("📚 Supporting passages:")
                for i, passage in enumerate(passages[:3]):  # Show top 3
                    print(f"  {i+1}. {passage[:200]}...")
                if len(passages) > 3:
                    print(f"  ... and {len(passages)-3} more passages")
            print()
            
        except KeyboardInterrupt:
            print("\n\n👋 Session interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error processing question: {str(e)}")
            print("Please try again or type 'help' for usage tips.\n")

def run_complete_hipporag2_pipeline(dataset_name="pdf_dataset", filename_pattern="", run_qa=True):
    """
    Run the complete pipeline from PDF processing to HippoRAG2 Q&A.
    
    Args:
        dataset_name (str): Name for the dataset
        filename_pattern (str): Pattern to match input files
        run_qa (bool): Whether to start interactive Q&A session
        
    Returns:
        HippoRAG2Retriever: Configured retriever (if successful)
    """
    
    print("🚀 Complete HippoRAG2 Pipeline: PDF to Q&A")
    print("=" * 60)
    print(f"Dataset: {dataset_name}")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # Step 1: Run the knowledge extraction pipeline
        print("Phase 1: Knowledge Graph Construction")
        print("-" * 40)
        success = run_knowledge_extraction_pipeline(dataset_name, filename_pattern)
        
        if not success:
            print("❌ Knowledge extraction failed. Cannot proceed to HippoRAG2 setup.")
            return None
            
        # Step 2: Setup HippoRAG2 retriever
        print("\nPhase 2: HippoRAG2 Setup")
        print("-" * 40)
        retriever = setup_hipporag2_retriever(dataset_name, filename_pattern)
        
        print("\n🎉 Complete pipeline setup successful!")
        print("=" * 60)
        print("✅ PDF processing completed")
        print("✅ Knowledge graph constructed") 
        print("✅ Embeddings and indexes created")
        print("✅ HippoRAG2 retriever ready")
        
        # Step 3: Interactive Q&A (optional)
        if run_qa:
            print("\nPhase 3: Interactive Q&A")
            print("-" * 40)
            hipporag2_interrogation(retriever)
        else:
            print("\n💡 To start Q&A session later, call:")
            print("hipporag2_interrogation(retriever)")
            
        return retriever
        
    except Exception as e:
        print(f"\n❌ Complete pipeline failed: {str(e)}")
        print("Check the error messages above for troubleshooting guidance")
        return None

if __name__ == "__main__":
    print("🎯 AutoSchemaKG Enhanced Pipeline")
    print("Using Gemini 2.5 Flash Direct API + Qwen3-Embedding-4B + HippoRAG2")
    print("From PDF Processing to Interactive Knowledge Graph Q&A")
    print()
    
    # Validate prerequisites
    if not validate_prerequisites():
        print("❌ Prerequisites not met. Please address the issues above.")
        exit(1)
    
    # Ask user for pipeline mode
    print("Choose pipeline mode:")
    print("1. Standard pipeline (extraction + embeddings)")
    print("2. Complete HippoRAG2 pipeline (extraction + embeddings + Q&A)")
    print("3. HippoRAG2 Q&A only (assumes extraction already completed)")
    
    try:
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == "1":
            # Standard pipeline
            print("\n🚀 Running standard knowledge extraction pipeline...")
            run_knowledge_extraction_pipeline()
            
        elif choice == "2":
            # Complete HippoRAG2 pipeline
            print("\n🚀 Running complete HippoRAG2 pipeline...")
            run_complete_hipporag2_pipeline()
            
        elif choice == "3":
            # HippoRAG2 Q&A only
            print("\n🦛 Setting up HippoRAG2 Q&A interface...")
            retriever = setup_hipporag2_retriever()
            hipporag2_interrogation(retriever)
            
        else:
            print("❌ Invalid choice. Running standard pipeline...")
            run_knowledge_extraction_pipeline()
            
    except KeyboardInterrupt:
        print("\n⏹️  Pipeline interrupted by user")
    except Exception as e:
        print(f"\n💥 Pipeline failed: {str(e)}")
        print("Check the error messages above for troubleshooting guidance")