{"permissions": {"allow": ["Bash(find:*)", "Bash(python -m pytest --help)", "<PERSON><PERSON>(python -m pip show:*)", "Bash(pip install:*)", "<PERSON><PERSON>(python:*)", "Bash(ls:*)", "Bash(TOKENIZERS_PARALLELISM=false python pdf_kg_extraction_pipeline.py)", "<PERSON><PERSON>(claude mcp:*)", "WebFetch(domain:docs.anthropic.com)", "<PERSON><PERSON>(env)", "mcp__smithery-ai-server-sequential-thinking__sequentialthinking", "mcp__upstash-context-7-mcp__resolve-library-id", "mcp__upstash-context-7-mcp__get-library-docs", "Bash(grep:*)", "<PERSON><PERSON>(git clone:*)", "Bash(cp:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(chmod:*)", "Bash(bash:*)", "Bash(rg:*)", "<PERSON><PERSON>(timeout:*)", "WebFetch(domain:ai.google.dev)", "Bash(\"/Users/<USER>/Library/Application Support/neo4j-desktop/Application/Data/dbmss/dbms-47e656e4-6473-4155-b149-3ffda6ca4945/bin/neo4j-admin\" database import full autoschemakg )", "Bash(--nodes=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/concept_csv/concept_nodes__from_json_with_concept.csv )", "Bash(--nodes=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/triples_csv/triple_nodes__from_json_without_emb.csv )", "Bash(--nodes=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/triples_csv/text_nodes__from_json.csv )", "Bash(--relationships=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/concept_csv/concept_edges__from_json_with_concept.csv )", "Bash(--relationships=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/triples_csv/triple_edges__from_json_without_emb.csv )", "Bash(--overwrite-destination )", "Bash(--multiline-fields=true )", "Bash(--id-type=string )", "Bash(--verbose )", "Bash(--skip-bad-relationships=true)", "Bash(java:*)", "Bash(/usr/libexec/java_home:*)", "Bash(JAVA_HOME=\"/Users/<USER>/Library/Application Support/neo4j-desktop/Application/Cache/runtime/zulu21.40.17-ca-jdk21.0.6-macosx_aarch64/zulu-21.jdk/Contents/Home\" )", "Bash(\"/Users/<USER>/Library/Application Support/neo4j-desktop/Application/Data/dbmss/dbms-47e656e4-6473-4155-b149-3ffda6ca4945/bin/neo4j-admin\" database import full autoschemakg )", "Bash(--nodes=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/concept_csv/concept_nodes__from_json_with_concept.csv )", "Bash(--nodes=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/triples_csv/triple_nodes__from_json_without_emb.csv )", "Bash(--nodes=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/triples_csv/text_nodes__from_json.csv )", "Bash(--relationships=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/concept_csv/concept_edges__from_json_with_concept.csv )", "Bash(--relationships=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/triples_csv/triple_edges__from_json_without_emb.csv )", "Bash(--overwrite-destination )", "Bash(--multiline-fields=true )", "Bash(--id-type=string )", "Bash(--verbose )", "Bash(JAVA_HOME=\"/Users/<USER>/Library/Application Support/neo4j-desktop/Application/Cache/runtime/zulu21.40.17-ca-jdk21.0.6-macosx_aarch64/zulu-21.jdk/Contents/Home\" )", "Bash(\"/Users/<USER>/Library/Application Support/neo4j-desktop/Application/Data/dbmss/dbms-47e656e4-6473-4155-b149-3ffda6ca4945/bin/neo4j-admin\" database import full autoschemakg )", "Bash(--nodes=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/concept_csv/concept_nodes__from_json_with_concept.csv )", "Bash(--nodes=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/triples_csv/triple_nodes__from_json_without_emb.csv )", "Bash(--nodes=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/triples_csv/text_nodes__from_json.csv )", "Bash(--relationships=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/concept_csv/concept_edges__from_json_with_concept.csv )", "Bash(--relationships=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/triples_csv/triple_edges__from_json_without_emb.csv )", "Bash(--overwrite-destination )", "Bash(--multiline-fields=true )", "Bash(--id-type=string )", "Bash(--verbose )", "Bash(JAVA_HOME=\"/Users/<USER>/Library/Application Support/neo4j-desktop/Application/Cache/runtime/zulu21.40.17-ca-jdk21.0.6-macosx_aarch64/zulu-21.jdk/Contents/Home\" )", "Bash(\"/Users/<USER>/Library/Application Support/neo4j-desktop/Application/Data/dbmss/dbms-47e656e4-6473-4155-b149-3ffda6ca4945/bin/neo4j-admin\" database import full autoschemakg )", "Bash(--nodes=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/concept_csv/concept_nodes__from_json_with_concept.csv )", "Bash(--nodes=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/triples_csv/triple_nodes__from_json_without_emb.csv )", "Bash(--nodes=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/triples_csv/text_nodes__from_json.csv )", "Bash(--relationships=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/concept_csv/concept_edges__from_json_with_concept.csv )", "Bash(--relationships=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/triples_csv/triple_edges__from_json_without_emb.csv )", "Bash(--overwrite-destination )", "Bash(--multiline-fields=true )", "Bash(--id-type=string )", "Bash(--format=standard )", "Bash(--verbose )", "Bash(JAVA_HOME=\"/Users/<USER>/Library/Application Support/neo4j-desktop/Application/Cache/runtime/zulu21.40.17-ca-jdk21.0.6-macosx_aarch64/zulu-21.jdk/Contents/Home\")", "Bash(export JAVA_HOME)", "Bash(\"/Users/<USER>/Library/Application Support/neo4j-desktop/Application/Data/dbmss/dbms-47e656e4-6473-4155-b149-3ffda6ca4945/bin/neo4j-admin\" database import full autoschemakg --nodes=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/concept_csv/concept_nodes__from_json_with_concept.csv --nodes=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/triples_csv/triple_nodes__from_json_without_emb.csv --nodes=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/triples_csv/text_nodes__from_json.csv --relationships=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/concept_csv/concept_edges__from_json_with_concept.csv --relationships=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/triples_csv/triple_edges__from_json_without_emb.csv --relationships=/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/360t_guide_direct_api_v2/triples_csv/text_edges__from_json.csv --overwrite-destination --multiline-fields=true --id-type=string --verbose --skip-bad-relationships=true --format=standard)", "<PERSON><PERSON>(mv:*)", "Bash(kill:*)", "<PERSON><PERSON>(jq:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(cat:*)"], "deny": []}}