"""
Direct Google Gen AI SDK Setup for Gemini 2.5 Flash
This script uses the new google-genai SDK for direct API access, bypassing OpenAI wrapper limitations.
"""

from google import genai
from atlas_rag.llm_generator import LLMGenerator
from configparser import ConfigParser
import json
import time
from typing import List, Dict, Any, Optional

class DirectGeminiLLMGenerator:
    """
    Direct Google Gen AI SDK wrapper that mimics LLMGenerator interface.
    This bypasses the OpenAI-compatible endpoint to access full context window.
    """
    
    def __init__(self, client: genai.Client, model_name: str):
        self.client = client
        self.model_name = model_name
        self.inference_type = "google_genai_direct"
        
    def _convert_messages_to_contents(self, messages: List[Dict[str, str]]) -> str:
        """Convert OpenAI-style messages to a single content string for Gemini."""
        content_parts = []
        
        for message in messages:
            role = message.get("role", "user")
            content = message.get("content", "")
            
            if role == "system":
                content_parts.append(f"System: {content}")
            elif role == "user":
                content_parts.append(f"User: {content}")
            elif role == "assistant":
                content_parts.append(f"Assistant: {content}")
        
        return "\n\n".join(content_parts)
    
    def generate_response(self, messages: List[Dict[str, str]], 
                         max_new_tokens: int = 8192,
                         temperature: float = 0.7,
                         **kwargs) -> str:
        """
        Generate response using direct Google Gen AI API.
        
        Args:
            messages: List of message dictionaries with 'role' and 'content'
            max_new_tokens: Maximum tokens to generate
            temperature: Temperature for generation
            
        Returns:
            Generated response text
        """
        try:
            # Convert messages to content string
            content = self._convert_messages_to_contents(messages)
            
            # Skip verbose token counting logging
            
            # Generate response using direct API
            start_time = time.time()
            
            response = self.client.models.generate_content(
                model=self.model_name,
                contents=content,
                config=genai.types.GenerateContentConfig(
                    max_output_tokens=max_new_tokens,
                    temperature=temperature,
                    candidate_count=1,
                    stop_sequences=None,
                )
            )
            
            time_cost = time.time() - start_time
            
            # Extract response text
            if response.candidates and len(response.candidates) > 0:
                candidate = response.candidates[0]
                if candidate.content and candidate.content.parts and len(candidate.content.parts) > 0:
                    content_text = candidate.content.parts[0].text or ""
                    
                    # Minimal success logging (removed verbose token details)
                    
                    return content_text
                elif candidate.content:
                    # Handle case where content exists but no parts
                    content_text = str(candidate.content)
                    return content_text
                else:
                    print("❌ No content in response candidate")
                    print(f"Candidate: {candidate}")
                    return ""
            else:
                print("❌ No candidates in response")
                print(f"Response: {response}")
                return ""
                
        except Exception as e:
            print(f"❌ Direct API error: {str(e)}")
            print(f"Error type: {type(e)}")
            
            # Return empty string to match LLMGenerator behavior
            return ""
    
    def generate_cot(self, question: str, max_new_tokens: int = 512) -> str:
        """Generate Chain-of-Thought response."""
        messages = [
            {"role": "system", "content": "You are a helpful assistant. Think step by step."},
            {"role": "user", "content": question}
        ]
        return self.generate_response(messages, max_new_tokens=max_new_tokens)
    
    def generate_with_context(self, question: str, context: str, max_new_tokens: int = 256) -> str:
        """Generate response with context."""
        messages = [
            {"role": "system", "content": "Answer the question based on the provided context."},
            {"role": "user", "content": f"Context: {context}\n\nQuestion: {question}"}
        ]
        return self.generate_response(messages, max_new_tokens=max_new_tokens)
    
    def ner(self, text: str) -> List[str]:
        """Named Entity Recognition."""
        messages = [
            {"role": "system", "content": "Extract named entities from the text. Return only the entities as a JSON list."},
            {"role": "user", "content": text}
        ]
        response = self.generate_response(messages, max_new_tokens=256)
        try:
            return json.loads(response)
        except:
            return []

def setup_gemini_direct_api():
    """
    Initialize Gemini 2.5 Flash using direct Google Gen AI SDK.
    
    Returns:
        DirectGeminiLLMGenerator: Configured direct API generator
    """
    
    # Load configuration
    config = ConfigParser()
    config.read('config.ini')
    
    # Get API key and model name from config
    api_key = config['settings']['GOOGLE_API_KEY']
    model_name = config['settings']['LLM_MODEL']
    
    print(f"🚀 Setting up Direct Google Gen AI SDK")
    print(f"Model: {model_name}")
    print(f"Using direct Google AI Studio API (bypassing OpenAI wrapper)")
    
    try:
        # Initialize direct Google Gen AI client
        client = genai.Client(api_key=api_key)
        
        # Test client connection
        print("🔍 Testing client connection...")
        try:
            models = client.models.list()
            available_models = [model.name for model in models][:5]  # Show first 5
            print(f"✅ Connection successful. Available models: {available_models}")
        except Exception as model_list_error:
            print(f"⚠️  Model listing failed (but connection may still work): {model_list_error}")
            print("✅ Proceeding with client setup...")
        
        # Create our custom LLM generator
        llm_generator = DirectGeminiLLMGenerator(client, model_name)
        
        print(f"✅ Successfully initialized {model_name} with direct API")
        print(f"Inference type: {llm_generator.inference_type}")
        
        return llm_generator
        
    except Exception as e:
        print(f"❌ Error setting up direct API generator: {str(e)}")
        raise e

def test_context_window_capacity(llm_generator):
    """
    Test the actual context window capacity with progressively larger inputs.
    
    Args:
        llm_generator: The direct API generator to test
    """
    print("\n🧪 Testing Context Window Capacity...")
    print("=" * 60)
    
    # Test different input sizes to find the actual context window limit
    test_sizes = [500, 1000, 2000, 5000, 10000, 20000, 50000, 100000]
    
    for size in test_sizes:
        print(f"\n📏 Testing with ~{size} tokens...")
        
        # Create test content of approximately the target size
        # Each word is roughly 1-2 tokens, so we'll use word count as approximation
        test_words = ["test", "word", "content", "example", "data"] * (size // 5)
        test_content = " ".join(test_words[:size//2])  # Approximate token count
        
        messages = [
            {"role": "system", "content": "You are a helpful assistant. Respond with a brief acknowledgment."},
            {"role": "user", "content": f"Please acknowledge this content: {test_content}"}
        ]
        
        try:
            start_time = time.time()
            response = llm_generator.generate_response(messages, max_new_tokens=100, temperature=0.1)
            duration = time.time() - start_time
            
            if response and len(response.strip()) > 0:
                print(f"✅ SUCCESS - {size} tokens processed in {duration:.2f}s")
                print(f"   Response: {response[:100]}...")
            else:
                print(f"❌ FAILED - Empty response at {size} tokens")
                break
                
        except Exception as e:
            print(f"❌ FAILED - Error at {size} tokens: {str(e)}")
            break
    
    print("\n📊 Context window testing completed")

def compare_apis_side_by_side():
    """
    Compare OpenAI wrapper vs Direct API performance with identical inputs.
    """
    print("\n🔬 Comparing OpenAI Wrapper vs Direct API...")
    print("=" * 60)
    
    # Load configuration
    config = ConfigParser()
    config.read('config.ini')
    api_key = config['settings']['GOOGLE_API_KEY']
    model_name = config['settings']['LLM_MODEL']
    
    # Test input that was failing with OpenAI wrapper
    test_messages = [
        {"role": "system", "content": "You are a knowledge extraction expert. Extract entities, relations, and events from the given text. Be concise and accurate."},
        {"role": "user", "content": "Apple Inc. is an American multinational technology company headquartered in Cupertino, California. It was founded by Steve Jobs, Steve Wozniak, and Ronald Wayne in April 1976. Apple is the world's largest technology company by revenue and is one of the world's most valuable companies. The company's hardware products include the iPhone smartphone, the iPad tablet computer, the Mac personal computer, and the Apple Watch smartwatch."}
    ]
    
    # Test with Direct API
    print("\n1️⃣ Testing Direct Google Gen AI API:")
    try:
        direct_client = genai.Client(api_key=api_key)
        direct_generator = DirectGeminiLLMGenerator(direct_client, model_name)
        
        start_time = time.time()
        direct_response = direct_generator.generate_response(test_messages, max_new_tokens=400)
        direct_duration = time.time() - start_time
        
        print(f"✅ Direct API Success")
        print(f"   Duration: {direct_duration:.2f}s")
        print(f"   Response length: {len(direct_response)} characters")
        print(f"   Response preview: {direct_response[:200]}...")
        
    except Exception as e:
        print(f"❌ Direct API Failed: {str(e)}")
    
    # Test with OpenAI Wrapper (for comparison)
    print("\n2️⃣ Testing OpenAI Wrapper (for comparison):")
    try:
        from setup_llm_generator import setup_gemini_llm_generator
        wrapper_generator = setup_gemini_llm_generator()
        
        start_time = time.time()
        wrapper_response = wrapper_generator._api_inference(test_messages, max_new_tokens=400)
        wrapper_duration = time.time() - start_time
        
        print(f"✅ Wrapper API Success")
        print(f"   Duration: {wrapper_duration:.2f}s")
        print(f"   Response length: {len(wrapper_response)} characters")
        print(f"   Response preview: {wrapper_response[:200]}...")
        
    except Exception as e:
        print(f"❌ Wrapper API Failed: {str(e)}")
    
    print("\n📊 API comparison completed")

if __name__ == "__main__":
    print("🚀 Setting up Direct Google Gen AI SDK for AutoSchemaKG...")
    
    # Setup the direct API generator
    llm_generator = setup_gemini_direct_api()
    
    # Test context window capacity
    test_context_window_capacity(llm_generator)
    
    # Compare APIs side by side
    compare_apis_side_by_side()
    
    print("\n✅ Direct Google Gen AI SDK setup completed successfully!")
    print("The direct API generator is ready for use in your knowledge graph extraction pipeline.")
    print("\n💡 Usage example:")
    print("from setup_gemini_direct import setup_gemini_direct_api")
    print("llm_generator = setup_gemini_direct_api()")