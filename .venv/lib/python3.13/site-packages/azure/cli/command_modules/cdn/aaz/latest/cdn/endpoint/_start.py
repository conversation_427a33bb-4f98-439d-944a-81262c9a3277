# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "cdn endpoint start",
)
class Start(AAZCommand):
    """Starts an existing CDN endpoint that is on a stopped state.

    :example: Start a CDN endpoint.
        az cdn endpoint start -g group -n endpoint --profile-name profile-name
    """

    _aaz_info = {
        "version": "2025-04-15",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.cdn/profiles/{}/endpoints/{}/start", "2025-04-15"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.endpoint_nameendpoint_name = AAZStrArg(
            options=["-n", "--name", "--endpoint-nameendpoint-name"],
            help="Name of the endpoint under the profile which is unique globally.",
            required=True,
            id_part="child_name_1",
        )
        _args_schema.profile_name = AAZStrArg(
            options=["--profile-name"],
            help="Name of the CDN profile which is unique within the resource group.",
            required=True,
            id_part="name",
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        yield self.EndpointsStart(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class EndpointsStart(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Cdn/profiles/{profileName}/endpoints/{endpointName}/start",
                **self.url_parameters
            )

        @property
        def method(self):
            return "POST"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "endpointName", self.ctx.args.endpoint_nameendpoint_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "profileName", self.ctx.args.profile_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-04-15",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _StartHelper._build_schema_endpoint_read(cls._schema_on_200)

            return cls._schema_on_200


class _StartHelper:
    """Helper class for Start"""

    _schema_endpoint_read = None

    @classmethod
    def _build_schema_endpoint_read(cls, _schema):
        if cls._schema_endpoint_read is not None:
            _schema.id = cls._schema_endpoint_read.id
            _schema.location = cls._schema_endpoint_read.location
            _schema.name = cls._schema_endpoint_read.name
            _schema.properties = cls._schema_endpoint_read.properties
            _schema.system_data = cls._schema_endpoint_read.system_data
            _schema.tags = cls._schema_endpoint_read.tags
            _schema.type = cls._schema_endpoint_read.type
            return

        cls._schema_endpoint_read = _schema_endpoint_read = AAZObjectType()

        endpoint_read = _schema_endpoint_read
        endpoint_read.id = AAZStrType(
            flags={"read_only": True},
        )
        endpoint_read.location = AAZStrType(
            flags={"required": True},
        )
        endpoint_read.name = AAZStrType(
            flags={"read_only": True},
        )
        endpoint_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        endpoint_read.system_data = AAZObjectType(
            serialized_name="systemData",
            flags={"read_only": True},
        )
        endpoint_read.tags = AAZDictType()
        endpoint_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_endpoint_read.properties
        properties.content_types_to_compress = AAZListType(
            serialized_name="contentTypesToCompress",
        )
        properties.custom_domains = AAZListType(
            serialized_name="customDomains",
            flags={"read_only": True},
        )
        properties.default_origin_group = AAZObjectType(
            serialized_name="defaultOriginGroup",
        )
        cls._build_schema_resource_reference_read(properties.default_origin_group)
        properties.delivery_policy = AAZObjectType(
            serialized_name="deliveryPolicy",
        )
        properties.geo_filters = AAZListType(
            serialized_name="geoFilters",
        )
        properties.host_name = AAZStrType(
            serialized_name="hostName",
            flags={"read_only": True},
        )
        properties.is_compression_enabled = AAZBoolType(
            serialized_name="isCompressionEnabled",
        )
        properties.is_http_allowed = AAZBoolType(
            serialized_name="isHttpAllowed",
        )
        properties.is_https_allowed = AAZBoolType(
            serialized_name="isHttpsAllowed",
        )
        properties.optimization_type = AAZStrType(
            serialized_name="optimizationType",
        )
        properties.origin_groups = AAZListType(
            serialized_name="originGroups",
        )
        properties.origin_host_header = AAZStrType(
            serialized_name="originHostHeader",
        )
        properties.origin_path = AAZStrType(
            serialized_name="originPath",
        )
        properties.origins = AAZListType(
            flags={"required": True},
        )
        properties.probe_path = AAZStrType(
            serialized_name="probePath",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.query_string_caching_behavior = AAZStrType(
            serialized_name="queryStringCachingBehavior",
        )
        properties.resource_state = AAZStrType(
            serialized_name="resourceState",
            flags={"read_only": True},
        )
        properties.url_signing_keys = AAZListType(
            serialized_name="urlSigningKeys",
        )
        properties.web_application_firewall_policy_link = AAZObjectType(
            serialized_name="webApplicationFirewallPolicyLink",
        )

        content_types_to_compress = _schema_endpoint_read.properties.content_types_to_compress
        content_types_to_compress.Element = AAZStrType()

        custom_domains = _schema_endpoint_read.properties.custom_domains
        custom_domains.Element = AAZObjectType()

        _element = _schema_endpoint_read.properties.custom_domains.Element
        _element.name = AAZStrType(
            flags={"required": True},
        )
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )

        properties = _schema_endpoint_read.properties.custom_domains.Element.properties
        properties.host_name = AAZStrType(
            serialized_name="hostName",
            flags={"required": True},
        )
        properties.validation_data = AAZStrType(
            serialized_name="validationData",
        )

        delivery_policy = _schema_endpoint_read.properties.delivery_policy
        delivery_policy.description = AAZStrType()
        delivery_policy.rules = AAZListType(
            flags={"required": True},
        )

        rules = _schema_endpoint_read.properties.delivery_policy.rules
        rules.Element = AAZObjectType()

        _element = _schema_endpoint_read.properties.delivery_policy.rules.Element
        _element.actions = AAZListType(
            flags={"required": True},
        )
        _element.conditions = AAZListType()
        _element.name = AAZStrType()
        _element.order = AAZIntType(
            flags={"required": True},
        )

        actions = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions
        actions.Element = AAZObjectType()

        _element = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element
        _element.name = AAZStrType(
            flags={"required": True},
        )

        disc_cache_expiration = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "CacheExpiration")
        disc_cache_expiration.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "CacheExpiration").parameters
        parameters.cache_behavior = AAZStrType(
            serialized_name="cacheBehavior",
            flags={"required": True},
        )
        parameters.cache_duration = AAZStrType(
            serialized_name="cacheDuration",
            nullable=True,
        )
        parameters.cache_type = AAZStrType(
            serialized_name="cacheType",
            flags={"required": True},
        )
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        disc_cache_key_query_string = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "CacheKeyQueryString")
        disc_cache_key_query_string.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "CacheKeyQueryString").parameters
        parameters.query_parameters = AAZStrType(
            serialized_name="queryParameters",
            nullable=True,
        )
        parameters.query_string_behavior = AAZStrType(
            serialized_name="queryStringBehavior",
            flags={"required": True},
        )
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        disc_modify_request_header = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "ModifyRequestHeader")
        disc_modify_request_header.parameters = AAZObjectType(
            flags={"required": True},
        )
        cls._build_schema_header_action_parameters_read(disc_modify_request_header.parameters)

        disc_modify_response_header = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "ModifyResponseHeader")
        disc_modify_response_header.parameters = AAZObjectType(
            flags={"required": True},
        )
        cls._build_schema_header_action_parameters_read(disc_modify_response_header.parameters)

        disc_origin_group_override = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "OriginGroupOverride")
        disc_origin_group_override.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "OriginGroupOverride").parameters
        parameters.origin_group = AAZObjectType(
            serialized_name="originGroup",
            flags={"required": True},
        )
        cls._build_schema_resource_reference_read(parameters.origin_group)
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        disc_route_configuration_override = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "RouteConfigurationOverride")
        disc_route_configuration_override.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "RouteConfigurationOverride").parameters
        parameters.cache_configuration = AAZObjectType(
            serialized_name="cacheConfiguration",
        )
        parameters.origin_group_override = AAZObjectType(
            serialized_name="originGroupOverride",
        )
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        cache_configuration = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "RouteConfigurationOverride").parameters.cache_configuration
        cache_configuration.cache_behavior = AAZStrType(
            serialized_name="cacheBehavior",
        )
        cache_configuration.cache_duration = AAZStrType(
            serialized_name="cacheDuration",
        )
        cache_configuration.is_compression_enabled = AAZStrType(
            serialized_name="isCompressionEnabled",
        )
        cache_configuration.query_parameters = AAZStrType(
            serialized_name="queryParameters",
        )
        cache_configuration.query_string_caching_behavior = AAZStrType(
            serialized_name="queryStringCachingBehavior",
        )

        origin_group_override = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "RouteConfigurationOverride").parameters.origin_group_override
        origin_group_override.forwarding_protocol = AAZStrType(
            serialized_name="forwardingProtocol",
        )
        origin_group_override.origin_group = AAZObjectType(
            serialized_name="originGroup",
        )
        cls._build_schema_resource_reference_read(origin_group_override.origin_group)

        disc_url_redirect = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "UrlRedirect")
        disc_url_redirect.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "UrlRedirect").parameters
        parameters.custom_fragment = AAZStrType(
            serialized_name="customFragment",
        )
        parameters.custom_hostname = AAZStrType(
            serialized_name="customHostname",
        )
        parameters.custom_path = AAZStrType(
            serialized_name="customPath",
        )
        parameters.custom_query_string = AAZStrType(
            serialized_name="customQueryString",
        )
        parameters.destination_protocol = AAZStrType(
            serialized_name="destinationProtocol",
        )
        parameters.redirect_type = AAZStrType(
            serialized_name="redirectType",
            flags={"required": True},
        )
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        disc_url_rewrite = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "UrlRewrite")
        disc_url_rewrite.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "UrlRewrite").parameters
        parameters.destination = AAZStrType(
            flags={"required": True},
        )
        parameters.preserve_unmatched_path = AAZBoolType(
            serialized_name="preserveUnmatchedPath",
        )
        parameters.source_pattern = AAZStrType(
            serialized_name="sourcePattern",
            flags={"required": True},
        )
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        disc_url_signing = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "UrlSigning")
        disc_url_signing.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "UrlSigning").parameters
        parameters.algorithm = AAZStrType()
        parameters.parameter_name_override = AAZListType(
            serialized_name="parameterNameOverride",
        )
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        parameter_name_override = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "UrlSigning").parameters.parameter_name_override
        parameter_name_override.Element = AAZObjectType()

        _element = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "UrlSigning").parameters.parameter_name_override.Element
        _element.param_indicator = AAZStrType(
            serialized_name="paramIndicator",
            flags={"required": True},
        )
        _element.param_name = AAZStrType(
            serialized_name="paramName",
            flags={"required": True},
        )

        conditions = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions
        conditions.Element = AAZObjectType()

        _element = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element
        _element.name = AAZStrType(
            flags={"required": True},
        )

        disc_client_port = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "ClientPort")
        disc_client_port.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "ClientPort").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "ClientPort").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "ClientPort").parameters.transforms
        transforms.Element = AAZStrType()

        disc_cookies = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "Cookies")
        disc_cookies.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "Cookies").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.selector = AAZStrType()
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "Cookies").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "Cookies").parameters.transforms
        transforms.Element = AAZStrType()

        disc_host_name = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "HostName")
        disc_host_name.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "HostName").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "HostName").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "HostName").parameters.transforms
        transforms.Element = AAZStrType()

        disc_http_version = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "HttpVersion")
        disc_http_version.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "HttpVersion").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "HttpVersion").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "HttpVersion").parameters.transforms
        transforms.Element = AAZStrType()

        disc_is_device = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "IsDevice")
        disc_is_device.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "IsDevice").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "IsDevice").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "IsDevice").parameters.transforms
        transforms.Element = AAZStrType()

        disc_post_args = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "PostArgs")
        disc_post_args.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "PostArgs").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.selector = AAZStrType()
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "PostArgs").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "PostArgs").parameters.transforms
        transforms.Element = AAZStrType()

        disc_query_string = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "QueryString")
        disc_query_string.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "QueryString").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "QueryString").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "QueryString").parameters.transforms
        transforms.Element = AAZStrType()

        disc_remote_address = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RemoteAddress")
        disc_remote_address.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RemoteAddress").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RemoteAddress").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RemoteAddress").parameters.transforms
        transforms.Element = AAZStrType()

        disc_request_body = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestBody")
        disc_request_body.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestBody").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestBody").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestBody").parameters.transforms
        transforms.Element = AAZStrType()

        disc_request_header = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestHeader")
        disc_request_header.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestHeader").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.selector = AAZStrType()
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestHeader").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestHeader").parameters.transforms
        transforms.Element = AAZStrType()

        disc_request_method = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestMethod")
        disc_request_method.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestMethod").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestMethod").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestMethod").parameters.transforms
        transforms.Element = AAZStrType()

        disc_request_scheme = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestScheme")
        disc_request_scheme.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestScheme").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestScheme").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestScheme").parameters.transforms
        transforms.Element = AAZStrType()

        disc_request_uri = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestUri")
        disc_request_uri.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestUri").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestUri").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestUri").parameters.transforms
        transforms.Element = AAZStrType()

        disc_server_port = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "ServerPort")
        disc_server_port.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "ServerPort").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "ServerPort").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "ServerPort").parameters.transforms
        transforms.Element = AAZStrType()

        disc_socket_addr = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "SocketAddr")
        disc_socket_addr.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "SocketAddr").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "SocketAddr").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "SocketAddr").parameters.transforms
        transforms.Element = AAZStrType()

        disc_ssl_protocol = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "SslProtocol")
        disc_ssl_protocol.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "SslProtocol").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "SslProtocol").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "SslProtocol").parameters.transforms
        transforms.Element = AAZStrType()

        disc_url_file_extension = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "UrlFileExtension")
        disc_url_file_extension.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "UrlFileExtension").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "UrlFileExtension").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "UrlFileExtension").parameters.transforms
        transforms.Element = AAZStrType()

        disc_url_file_name = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "UrlFileName")
        disc_url_file_name.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "UrlFileName").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "UrlFileName").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "UrlFileName").parameters.transforms
        transforms.Element = AAZStrType()

        disc_url_path = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "UrlPath")
        disc_url_path.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "UrlPath").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "UrlPath").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "UrlPath").parameters.transforms
        transforms.Element = AAZStrType()

        geo_filters = _schema_endpoint_read.properties.geo_filters
        geo_filters.Element = AAZObjectType()

        _element = _schema_endpoint_read.properties.geo_filters.Element
        _element.action = AAZStrType(
            flags={"required": True},
        )
        _element.country_codes = AAZListType(
            serialized_name="countryCodes",
            flags={"required": True},
        )
        _element.relative_path = AAZStrType(
            serialized_name="relativePath",
            flags={"required": True},
        )

        country_codes = _schema_endpoint_read.properties.geo_filters.Element.country_codes
        country_codes.Element = AAZStrType()

        origin_groups = _schema_endpoint_read.properties.origin_groups
        origin_groups.Element = AAZObjectType()

        _element = _schema_endpoint_read.properties.origin_groups.Element
        _element.name = AAZStrType(
            flags={"required": True},
        )
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )

        properties = _schema_endpoint_read.properties.origin_groups.Element.properties
        properties.health_probe_settings = AAZObjectType(
            serialized_name="healthProbeSettings",
        )
        properties.origins = AAZListType(
            flags={"required": True},
        )
        properties.response_based_origin_error_detection_settings = AAZObjectType(
            serialized_name="responseBasedOriginErrorDetectionSettings",
        )
        properties.traffic_restoration_time_to_healed_or_new_endpoints_in_minutes = AAZIntType(
            serialized_name="trafficRestorationTimeToHealedOrNewEndpointsInMinutes",
        )

        health_probe_settings = _schema_endpoint_read.properties.origin_groups.Element.properties.health_probe_settings
        health_probe_settings.probe_interval_in_seconds = AAZIntType(
            serialized_name="probeIntervalInSeconds",
        )
        health_probe_settings.probe_path = AAZStrType(
            serialized_name="probePath",
        )
        health_probe_settings.probe_protocol = AAZStrType(
            serialized_name="probeProtocol",
        )
        health_probe_settings.probe_request_type = AAZStrType(
            serialized_name="probeRequestType",
        )

        origins = _schema_endpoint_read.properties.origin_groups.Element.properties.origins
        origins.Element = AAZObjectType()
        cls._build_schema_resource_reference_read(origins.Element)

        response_based_origin_error_detection_settings = _schema_endpoint_read.properties.origin_groups.Element.properties.response_based_origin_error_detection_settings
        response_based_origin_error_detection_settings.http_error_ranges = AAZListType(
            serialized_name="httpErrorRanges",
        )
        response_based_origin_error_detection_settings.response_based_detected_error_types = AAZStrType(
            serialized_name="responseBasedDetectedErrorTypes",
        )
        response_based_origin_error_detection_settings.response_based_failover_threshold_percentage = AAZIntType(
            serialized_name="responseBasedFailoverThresholdPercentage",
        )

        http_error_ranges = _schema_endpoint_read.properties.origin_groups.Element.properties.response_based_origin_error_detection_settings.http_error_ranges
        http_error_ranges.Element = AAZObjectType()

        _element = _schema_endpoint_read.properties.origin_groups.Element.properties.response_based_origin_error_detection_settings.http_error_ranges.Element
        _element.begin = AAZIntType()
        _element.end = AAZIntType()

        origins = _schema_endpoint_read.properties.origins
        origins.Element = AAZObjectType()

        _element = _schema_endpoint_read.properties.origins.Element
        _element.name = AAZStrType(
            flags={"required": True},
        )
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )

        properties = _schema_endpoint_read.properties.origins.Element.properties
        properties.enabled = AAZBoolType()
        properties.host_name = AAZStrType(
            serialized_name="hostName",
            flags={"required": True},
        )
        properties.http_port = AAZIntType(
            serialized_name="httpPort",
        )
        properties.https_port = AAZIntType(
            serialized_name="httpsPort",
        )
        properties.origin_host_header = AAZStrType(
            serialized_name="originHostHeader",
        )
        properties.priority = AAZIntType()
        properties.private_endpoint_status = AAZStrType(
            serialized_name="privateEndpointStatus",
            flags={"read_only": True},
        )
        properties.private_link_alias = AAZStrType(
            serialized_name="privateLinkAlias",
        )
        properties.private_link_approval_message = AAZStrType(
            serialized_name="privateLinkApprovalMessage",
        )
        properties.private_link_location = AAZStrType(
            serialized_name="privateLinkLocation",
        )
        properties.private_link_resource_id = AAZStrType(
            serialized_name="privateLinkResourceId",
        )
        properties.weight = AAZIntType()

        url_signing_keys = _schema_endpoint_read.properties.url_signing_keys
        url_signing_keys.Element = AAZObjectType()

        _element = _schema_endpoint_read.properties.url_signing_keys.Element
        _element.key_id = AAZStrType(
            serialized_name="keyId",
            flags={"required": True},
        )
        _element.key_source_parameters = AAZObjectType(
            serialized_name="keySourceParameters",
            flags={"required": True},
        )

        key_source_parameters = _schema_endpoint_read.properties.url_signing_keys.Element.key_source_parameters
        key_source_parameters.resource_group_name = AAZStrType(
            serialized_name="resourceGroupName",
            flags={"required": True},
        )
        key_source_parameters.secret_name = AAZStrType(
            serialized_name="secretName",
            flags={"required": True},
        )
        key_source_parameters.secret_version = AAZStrType(
            serialized_name="secretVersion",
            flags={"required": True},
        )
        key_source_parameters.subscription_id = AAZStrType(
            serialized_name="subscriptionId",
            flags={"required": True},
        )
        key_source_parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )
        key_source_parameters.vault_name = AAZStrType(
            serialized_name="vaultName",
            flags={"required": True},
        )

        web_application_firewall_policy_link = _schema_endpoint_read.properties.web_application_firewall_policy_link
        web_application_firewall_policy_link.id = AAZStrType()

        system_data = _schema_endpoint_read.system_data
        system_data.created_at = AAZStrType(
            serialized_name="createdAt",
        )
        system_data.created_by = AAZStrType(
            serialized_name="createdBy",
        )
        system_data.created_by_type = AAZStrType(
            serialized_name="createdByType",
        )
        system_data.last_modified_at = AAZStrType(
            serialized_name="lastModifiedAt",
        )
        system_data.last_modified_by = AAZStrType(
            serialized_name="lastModifiedBy",
        )
        system_data.last_modified_by_type = AAZStrType(
            serialized_name="lastModifiedByType",
        )

        tags = _schema_endpoint_read.tags
        tags.Element = AAZStrType()

        _schema.id = cls._schema_endpoint_read.id
        _schema.location = cls._schema_endpoint_read.location
        _schema.name = cls._schema_endpoint_read.name
        _schema.properties = cls._schema_endpoint_read.properties
        _schema.system_data = cls._schema_endpoint_read.system_data
        _schema.tags = cls._schema_endpoint_read.tags
        _schema.type = cls._schema_endpoint_read.type

    _schema_header_action_parameters_read = None

    @classmethod
    def _build_schema_header_action_parameters_read(cls, _schema):
        if cls._schema_header_action_parameters_read is not None:
            _schema.header_action = cls._schema_header_action_parameters_read.header_action
            _schema.header_name = cls._schema_header_action_parameters_read.header_name
            _schema.type_name = cls._schema_header_action_parameters_read.type_name
            _schema.value = cls._schema_header_action_parameters_read.value
            return

        cls._schema_header_action_parameters_read = _schema_header_action_parameters_read = AAZObjectType()

        header_action_parameters_read = _schema_header_action_parameters_read
        header_action_parameters_read.header_action = AAZStrType(
            serialized_name="headerAction",
            flags={"required": True},
        )
        header_action_parameters_read.header_name = AAZStrType(
            serialized_name="headerName",
            flags={"required": True},
        )
        header_action_parameters_read.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )
        header_action_parameters_read.value = AAZStrType()

        _schema.header_action = cls._schema_header_action_parameters_read.header_action
        _schema.header_name = cls._schema_header_action_parameters_read.header_name
        _schema.type_name = cls._schema_header_action_parameters_read.type_name
        _schema.value = cls._schema_header_action_parameters_read.value

    _schema_resource_reference_read = None

    @classmethod
    def _build_schema_resource_reference_read(cls, _schema):
        if cls._schema_resource_reference_read is not None:
            _schema.id = cls._schema_resource_reference_read.id
            return

        cls._schema_resource_reference_read = _schema_resource_reference_read = AAZObjectType()

        resource_reference_read = _schema_resource_reference_read
        resource_reference_read.id = AAZStrType()

        _schema.id = cls._schema_resource_reference_read.id


__all__ = ["Start"]
