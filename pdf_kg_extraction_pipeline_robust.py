"""
Robust PDF Knowledge Graph Extraction Pipeline

Enhanced version of the PDF KG extraction pipeline with comprehensive resumability,
error handling, and progress tracking capabilities.

Features:
- Resume functionality from any failure point
- Comprehensive error handling with retry logic
- Real-time progress tracking with individual file monitoring  
- State management with checkpoint/restore
- Command-line arguments for flexible operation modes
"""

import os
import sys
import argparse
import time
from datetime import datetime
from typing import Optional

from setup_llm_generator_direct import setup_gemini_llm_generator_direct
from setup_embedding_model import setup_qwen_embedding_model
from setup_processing_config import create_processing_config, create_output_directories

from robust_kg_extractor import RobustKGExtractor
from error_handler import ErrorConfig
from pipeline_state_manager import PipelineStateManager
from progress_tracker import ProgressTracker


def create_robust_processing_config(dataset_name: str = "pdf_dataset", 
                                   filename_pattern: str = "",
                                   tier1_mode: bool = False) -> 'ProcessingConfig':
    """
    Create processing configuration optimized for robust pipeline operation.
    
    Args:
        dataset_name: Name for the output dataset directory
        filename_pattern: Pattern to match input files
        tier1_mode: Use Tier 1 optimized settings for token limits
        
    Returns:
        ProcessingConfig: Configured processing parameters
    """
    from setup_processing_config import create_processing_config_tier1, create_processing_config
    
    if tier1_mode:
        print("🔧 Using Tier 1 optimized configuration for token limits")
        config = create_processing_config_tier1(dataset_name, filename_pattern)
        # Further optimize for robustness
        config.batch_size_concept = 5  # Even smaller for reliability
        config.max_workers = 1  # Single worker to avoid rate limits
    else:
        print("🔧 Using standard configuration with robust optimizations")
        config = create_processing_config(dataset_name, filename_pattern)
        # Optimize for robustness
        config.batch_size_triple = 8   # Smaller batches for stability
        config.batch_size_concept = 16 # Reduced for reliability
        config.max_workers = 2         # Reduced to avoid rate limits
    
    return config


def create_error_config(enable_notifications: bool = False,
                       notification_email: str = "",
                       max_retries: int = 3) -> ErrorConfig:
    """
    Create error handling configuration.
    
    Args:
        enable_notifications: Whether to enable email notifications
        notification_email: Email address for notifications
        max_retries: Maximum number of retry attempts
        
    Returns:
        ErrorConfig: Configured error handling parameters
    """
    return ErrorConfig(
        max_retries=max_retries,
        base_delay=2.0,          # Start with 2-second delays
        max_delay=300.0,         # Max 5-minute delays
        backoff_multiplier=2.0,  # Exponential backoff
        jitter=True,             # Add randomness to prevent thundering herd
        circuit_breaker_threshold=5,  # Open circuit after 5 failures
        circuit_breaker_timeout=300.0,  # Try again after 5 minutes
        enable_notifications=enable_notifications,
        notification_email=notification_email
    )


def run_robust_knowledge_extraction_pipeline(dataset_name: str = "pdf_dataset",
                                            filename_pattern: str = "",
                                            force_restart: bool = False,
                                            retry_failed: bool = False,
                                            tier1_mode: bool = False,
                                            enable_notifications: bool = False,
                                            notification_email: str = "",
                                            max_retries: int = 3) -> bool:
    """
    Run the robust knowledge graph extraction pipeline.
    
    Args:
        dataset_name: Name for the dataset
        filename_pattern: Pattern to match input files
        force_restart: Ignore existing state and start fresh
        retry_failed: Retry previously failed operations
        tier1_mode: Use Tier 1 optimized settings for token limits
        enable_notifications: Enable email notifications for errors
        notification_email: Email address for notifications
        max_retries: Maximum number of retry attempts
        
    Returns:
        True if pipeline completed successfully, False otherwise
    """
    
    print("🚀 Starting Robust AutoSchemaKG Pipeline")
    print("=" * 60)
    print(f"Dataset: {dataset_name}")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Mode: {'Tier 1 Optimized' if tier1_mode else 'Standard'}")
    print(f"Force Restart: {force_restart}")
    print(f"Retry Failed: {retry_failed}")
    print()
    
    start_time = time.time()
    
    try:
        # Step 1: Initialize models
        print("1️⃣ Initializing Models...")
        print("-" * 30)
        
        llm_generator = setup_gemini_llm_generator_direct()
        sentence_encoder = setup_qwen_embedding_model()
        
        print("✅ Models initialized successfully")
        print(f"   LLM: {llm_generator.model_name}")
        print(f"   Embedding: {sentence_encoder.model_name}")
        print()
        
        # Step 2: Setup configuration
        print("2️⃣ Setting up Enhanced Configuration...")
        print("-" * 30)
        
        create_output_directories(dataset_name)
        processing_config = create_robust_processing_config(
            dataset_name, filename_pattern, tier1_mode
        )
        
        error_config = create_error_config(
            enable_notifications=enable_notifications,
            notification_email=notification_email,
            max_retries=max_retries
        )
        
        print("✅ Configuration setup complete")
        print(f"   Batch size (triples): {processing_config.batch_size_triple}")
        print(f"   Batch size (concepts): {processing_config.batch_size_concept}")
        print(f"   Max retries: {error_config.max_retries}")
        print(f"   Output directory: {processing_config.output_directory}")
        print()
        
        # Step 3: Initialize robust extractor
        print("3️⃣ Initializing Robust Knowledge Graph Extractor...")
        print("-" * 30)
        
        robust_extractor = RobustKGExtractor(
            model=llm_generator,
            config=processing_config,
            resume_enabled=True,
            error_config=error_config,
            progress_log_level="INFO"
        )
        
        print("✅ Robust extractor initialized successfully")
        print("   Features enabled:")
        print("   - ✅ Resume functionality")
        print("   - ✅ Comprehensive error handling")
        print("   - ✅ Real-time progress tracking")
        print("   - ✅ State management with checkpoints")
        print()
        
        # Step 4: Run complete robust pipeline
        print("4️⃣ Running Complete Robust Pipeline...")
        print("-" * 30)
        print("🎯 This enhanced pipeline will:")
        print("   - Resume from any previous failure point")
        print("   - Provide detailed progress tracking")
        print("   - Automatically retry failed operations")
        print("   - Save checkpoints every 5 minutes")
        print("   - Handle API rate limits gracefully")
        print()
        
        pipeline_start = time.time()
        
        success = robust_extractor.run_complete_pipeline(
            sentence_encoder=sentence_encoder,
            force_restart=force_restart,
            retry_failed=retry_failed
        )
        
        pipeline_time = time.time() - pipeline_start
        total_time = time.time() - start_time
        
        if success:
            print("\n🎉 ROBUST PIPELINE COMPLETED SUCCESSFULLY!")
            print("=" * 60)
            print(f"Pipeline processing time: {pipeline_time/60:.1f} minutes")
            print(f"Total execution time: {total_time/60:.1f} minutes")
            print(f"Dataset location: {processing_config.output_directory}")
            
            # Display processing summary
            summary = robust_extractor.get_pipeline_summary()
            print(f"\n📊 Processing Summary:")
            print(f"   Files processed: {summary['input_files_count']}")
            print(f"   Success rate: {summary['progress_tracker']['overall_progress']:.1f}%")
            print(f"   Total errors: {summary['error_handler']['total_errors']}")
            print(f"   Resolved errors: {summary['error_handler']['resolved_errors']}")
            
            print("\n📋 Next Steps:")
            print("1. Review the generated files in the dataset directory")
            print("2. Stop your Neo4j autoschemakg database")
            print("3. Run the Neo4j import command (check neo4j_import_commands.txt)")
            print("4. Start your Neo4j autoschemakg database")
            print("5. Test the knowledge graph with RAG queries")
            
            return True
            
        else:
            print("\n❌ ROBUST PIPELINE FAILED")
            print("=" * 60)
            print(f"Total execution time: {total_time/60:.1f} minutes")
            
            # Display error summary
            summary = robust_extractor.get_pipeline_summary()
            print(f"\n🚨 Error Summary:")
            print(f"   Total errors: {summary['error_handler']['total_errors']}")
            print(f"   Unresolved errors: {summary['error_handler']['unresolved_errors']}")
            print(f"   Last stage: {summary['current_stage']}")
            
            print("\n🔧 Recovery Options:")
            print("1. Run with --retry-failed to retry failed operations")
            print("2. Check the detailed error logs for specific issues")
            print("3. Run with --force-restart to start completely fresh")
            print("4. Verify your API keys and network connectivity")
            
            return False
        
    except Exception as e:
        total_time = time.time() - start_time
        print(f"\n💥 PIPELINE INITIALIZATION FAILED: {str(e)}")
        print("=" * 60)
        print(f"Execution time: {total_time/60:.1f} minutes")
        print()
        print("🔧 Troubleshooting tips:")
        print("1. Check your API keys in config.ini")
        print("2. Ensure input files are in example_data/ directory")
        print("3. Verify internet connection for API calls")
        print("4. Check Python dependencies are installed")
        
        return False


def generate_import_summary(dataset_name: str):
    """
    Generate a summary of files created and Neo4j import commands.
    """
    import_dir = f"import/{dataset_name}"
    
    # Count files
    csv_files = []
    index_files = []
    
    for root, dirs, files in os.walk(import_dir):
        for file in files:
            if file.endswith('.csv'):
                csv_files.append(os.path.join(root, file))
            elif file.endswith('.index'):
                index_files.append(os.path.join(root, file))
    
    print(f"\n📊 Import Summary for {dataset_name}:")
    print(f"  - CSV files created: {len(csv_files)}")
    print(f"  - FAISS indexes created: {len(index_files)}")
    
    # Generate Neo4j import command
    neo4j_command = generate_neo4j_import_command(dataset_name, csv_files)
    
    # Save import commands to file
    with open("neo4j_import_commands.txt", "w") as f:
        f.write("Neo4j Import Commands for AutoSchemaKG (Robust Pipeline)\n")
        f.write("=" * 60 + "\n\n")
        f.write("IMPORTANT: Stop your autoschemakg database before running this command!\n\n")
        f.write("Command to run in your Neo4j installation directory:\n")
        f.write("-" * 50 + "\n")
        f.write(neo4j_command)
        f.write("\n\nAfter import completes, start your autoschemakg database again.\n")
        f.write("\nGenerated by Robust PDF KG Extraction Pipeline\n")
        f.write(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    print("✅ Neo4j import commands saved to neo4j_import_commands.txt")


def generate_neo4j_import_command(dataset_name: str, csv_files: list) -> str:
    """Generate the Neo4j admin import command."""
    # Categorize CSV files
    node_files = []
    relationship_files = []
    
    for file in csv_files:
        if 'nodes' in file or 'node' in file:
            node_files.append(file)
        elif 'edges' in file or 'edge' in file:
            relationship_files.append(file)
    
    # Build command
    command_parts = [
        "neo4j-admin database import full autoschemakg"
    ]
    
    # Add node files
    for node_file in node_files:
        command_parts.append(f"    --nodes ./{node_file}")
    
    # Add relationship files  
    for rel_file in relationship_files:
        command_parts.append(f"    --relationships ./{rel_file}")
    
    # Add options
    command_parts.extend([
        "    --overwrite-destination",
        "    --multiline-fields=true", 
        "    --id-type=string",
        "    --verbose",
        "    --skip-bad-relationships=true"
    ])
    
    return " \\\n".join(command_parts)


def validate_prerequisites():
    """Validate that all prerequisites are met before running the pipeline."""
    print("🔍 Validating Prerequisites...")
    print("-" * 30)
    
    # Check config file
    if not os.path.exists('config.ini'):
        print("❌ config.ini not found")
        return False
    print("✅ config.ini found")
    
    # Check input directory
    if not os.path.exists('example_data'):
        print("❌ example_data directory not found")
        print("💡 Create this directory and place your JSON files there")
        return False
    
    # Check for input files
    input_files = [f for f in os.listdir('example_data') if f.endswith(('.json', '.txt'))]
    if len(input_files) == 0:
        print("❌ No input files found in example_data/")
        print("💡 Place your processed PDF files (JSON format) in example_data/")
        return False
    
    print(f"✅ Found {len(input_files)} input files")
    
    # Check import directory exists
    os.makedirs('import', exist_ok=True)
    print("✅ Import directory ready")
    
    print("✅ All prerequisites validated\n")
    return True


def show_usage_examples():
    """Display usage examples for the robust pipeline."""
    print("\n💡 Usage Examples:")
    print("=" * 50)
    print("1. Standard run (with resume capability):")
    print("   python pdf_kg_extraction_pipeline_robust.py")
    print()
    print("2. Force restart from beginning:")
    print("   python pdf_kg_extraction_pipeline_robust.py --force-restart")
    print()
    print("3. Retry only failed operations:")
    print("   python pdf_kg_extraction_pipeline_robust.py --retry-failed")
    print()
    print("4. Tier 1 optimized (for token limits):")
    print("   python pdf_kg_extraction_pipeline_robust.py --tier1")
    print()
    print("5. With email notifications:")
    print("   python pdf_kg_extraction_pipeline_robust.py --notifications --email <EMAIL>")
    print()
    print("6. Custom dataset name:")
    print("   python pdf_kg_extraction_pipeline_robust.py --dataset my_custom_dataset")


def main():
    """Main entry point for the robust pipeline."""
    parser = argparse.ArgumentParser(
        description='Robust PDF Knowledge Graph Extraction Pipeline',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                          # Standard run with resume capability
  %(prog)s --force-restart          # Start fresh, ignore existing progress
  %(prog)s --retry-failed           # Retry only previously failed operations
  %(prog)s --tier1                  # Use Tier 1 optimized settings
  %(prog)s --notifications --email <EMAIL>  # Enable email alerts
        """
    )
    
    parser.add_argument('--dataset', '-d', 
                       default='pdf_dataset',
                       help='Dataset name for output directory (default: pdf_dataset)')
    
    parser.add_argument('--pattern', '-p',
                       default='',
                       help='Filename pattern to match input files (default: all files)')
    
    parser.add_argument('--force-restart', '-f',
                       action='store_true',
                       help='Ignore existing state and start fresh')
    
    parser.add_argument('--retry-failed', '-r',
                       action='store_true',
                       help='Retry previously failed operations')
    
    parser.add_argument('--tier1', '-t',
                       action='store_true',
                       help='Use Tier 1 optimized settings for token limits')
    
    parser.add_argument('--notifications', '-n',
                       action='store_true',
                       help='Enable email notifications for critical errors')
    
    parser.add_argument('--email', '-e',
                       default='',
                       help='Email address for notifications (requires --notifications)')
    
    parser.add_argument('--max-retries', '-m',
                       type=int,
                       default=3,
                       help='Maximum number of retry attempts (default: 3)')
    
    parser.add_argument('--examples',
                       action='store_true',
                       help='Show usage examples and exit')
    
    args = parser.parse_args()
    
    if args.examples:
        show_usage_examples()
        return
    
    print("🎯 Robust AutoSchemaKG Pipeline")
    print("Enhanced with resumability, error handling, and progress tracking")
    print("From PDF Processing to Interactive Knowledge Graph Q&A")
    print()
    
    # Validate prerequisites
    if not validate_prerequisites():
        print("❌ Prerequisites not met. Please address the issues above.")
        sys.exit(1)
    
    # Validate email configuration
    if args.notifications and not args.email:
        print("❌ Email address required when --notifications is enabled")
        print("Use --email <EMAIL>")
        sys.exit(1)
    
    try:
        # Run the robust pipeline
        success = run_robust_knowledge_extraction_pipeline(
            dataset_name=args.dataset,
            filename_pattern=args.pattern,
            force_restart=args.force_restart,
            retry_failed=args.retry_failed,
            tier1_mode=args.tier1,
            enable_notifications=args.notifications,
            notification_email=args.email,
            max_retries=args.max_retries
        )
        
        if success:
            # Generate import summary
            generate_import_summary(args.dataset)
            print("\n🎉 Pipeline completed successfully!")
            sys.exit(0)
        else:
            print("\n❌ Pipeline failed. Check logs for details.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️ Pipeline interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()